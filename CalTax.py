def up2(m):
    a = divmod(m, int(m))
    b = round(a[1], 2)
    b = "%.2f" % b
    if not str(b)[-1] == '0':
        c = int(str(b)[-2]) + 1
        c = c / 10
        m = int(m) + c
    return m


def getfour(socbase):
    old = up2(socbase * 0.08)
    medicne = up2(socbase * 0.02)
    lost = up2(socbase * 0.005)
    house = round((socbase * 0.07))
    fourcoin = old + medicne + lost + house
    return fourcoin


def calget(sallist):
    thelength = len(sallist)
    socbaselist = [17000, 20000]
    CoinBeforeAug = getfour(socbaselist[0])
    CoinAfterAug = getfour(socbaselist[1])
    print(f'8月前三险一金是{CoinBeforeAug}，后面是{CoinAfterAug}')
    tax = [[36000, 0.03, 0],
           [144000, 0.1, 2520],
           [300000, 0.2, 16920],
           [420000, 0.25, 31920],
           [660000, 0.3, 52920],
           [960000, 0.35, 85920],
           [9999999, 0.45, 181920]]
    monthgotlist = []
    taxlist = []
    moneygots = []
    for i in range(1, thelength + 1):
        fourcoin = CoinBeforeAug if i < 8 else CoinAfterAug
        monthgot = sallist[i - 1] - fourcoin - 5000
        monthgotlist.append(monthgot)
        taxx = 0
        minm = 0
        for h in range(0, 7):
            if sum(monthgotlist[0:i]) <= tax[h][0]:
                taxx = tax[h][1]
                minm = tax[h][2]
                break
        monthtax = sum(monthgotlist[0:i + 1]) * taxx - minm - sum(taxlist[0:i])
        monthtax = round(monthtax, 2)
        taxlist.append(monthtax)
        salaftertax = monthgot + 5000 - monthtax
        moneygots.append(salaftertax)
    print("全年所得", sum(moneygots))
    print("总税：",sum(taxlist))
    print("每月预扣额：", monthgotlist)
    print("每月个税：", taxlist)
    print("每月到手:", moneygots)


sallist = [12345 for i in range(12)]
calget(sallist)
