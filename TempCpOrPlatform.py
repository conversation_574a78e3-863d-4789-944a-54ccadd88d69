import subprocess, threading, hashlib
import requests, json, time, datetime, random, os

# 需安装ffmpeg,参考网址 https://zhuanlan.zhihu.com/p/324472015

headers = dict()

platforms = [['pt69582870', 'U0CxFXosNZlkgEncvG0KRhffKEEswROZ'],  # 弹丸qa
             ['pt31120714', 'mI4bwbAiKrXgQ7yygpJnYWJc77CbfSJe'],  # 狼人杀qa1
             ['pt02038168', 'pK5oZvfqAzAe4Gb4zEDgcBJbMX2Sq3z7'],  # 狼人杀沙盒
             ['pt87345622', 'pKwPMDpjBck7IFGJxLSq1GNIIq1httbL'],  # 弹丸沙盒
             ]
appids = [
    ['yg91688886', 'H517stt3ZWS1nbGuNWw9hTQnXtG5D7xx'],  # demo小游戏qa
    ['yg17823998', 'GcX80tqv7bLTZeeGCqSA9gWq5yNt65Hr'],  # 丛林求生qa
    # ['yg10226371', 'gAA2bN0geJJJcbrCqO4oqQGdrZGbFsvk'],  # 都市求生qa
    # ['yg33863447', 'FyuVgOIgaRvGAbh9Bzsw059bAONSVcbN'],  # demo小游戏沙盒
]
host = 'https://qa.live.caniculab.com/live'  # qa环境
# host = 'https://sd.live.caniculab.com/live' # 沙盒环境

phs = ['14725836907']
# phs = [f'155500000{x}' for x in range(70, 96)]  # 对应主播手机号，
files = r'D:\download\videos'  # 推流视频文件夹
platform = platforms[0]  # 主播平台选择


# 挂载游戏todo=1 仅直播todo=0
# run(phs, files, appids, 0)

# 查询录播地址
def streamrecord(streamname):
    url = host + '/platform/live/stream/record'
    params = {'streamName': streamname,
              }
    ns = streamname + 'U0CxFXosNZlkgEncvG0KRhffKEEswROZ'
    sign = hashlib.md5(ns.encode()).hexdigest()
    headers = {'live-platform-id': 'pt69582870',
               'live-signature': sign}
    response = requests.get(url, headers=headers, params=params)
    print(response.text)


# streamrecord('daed94ec00144af195bb14b5d8555971')


def ptstartlive():
    url = host + '/platform/live/start'
    params = {
        "expireTimeMinute": 1440,
        "livingRoomId": "100136",
        "phoneNumber": "14725836905",
        "platformAvatar": "http://dev-qiniu.pland.caniculab.com/pland/public/userHeader/12982CA4-F916-40E6-BBE7-A51EA4999AA7IMG_2195.PNG?imageView2/5/w/140/h/140%7CroundPic/radius/70",
        "platformNickname": "5鬼",
        "platformUid": "104103",
        "pushExpireTimeMinute": 4320,
        "status": 0
    }
    ns = json.dumps(params) + 'U0CxFXosNZlkgEncvG0KRhffKEEswROZ'
    sign = hashlib.md5(ns.encode()).hexdigest()
    headers = {'live-platform-id': 'pt69582870',
               'live-signature': sign}
    response = requests.post(url, headers=headers, json=params)
    print(response.text)


# ptstartlive()


def ptstoplive():
    url = host + '/platform/live/stop'
    params = {
        "platformUid": "103008",
        "timestamp": int(time.time() * 1000)
    }
    ns = json.dumps(params) + 'U0CxFXosNZlkgEncvG0KRhffKEEswROZ'
    sign = hashlib.md5(ns.encode()).hexdigest()
    headers = {'live-platform-id': 'pt69582870',
               'live-signature': sign}
    response = requests.post(url, headers=headers, params=params)
    print(response.text)
# ptstoplive()
