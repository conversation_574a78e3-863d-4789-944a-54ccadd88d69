import socket
import threading
from concurrent.futures import ThreadPoolExecutor
import time

HOST = '127.0.0.1'  # 服务器ip地址
PORT = 9001  # 服务器端口号
BUFFER_SIZE = 1024  # 每次接收消息的缓存大小，单位为字节

def send_message(client_socket):
    """向服务器发送消息"""
    while True:
        a=f'当前线程名称：{threading.current_thread().name}'
        message = a+'&'+str(time.time())  # 从控制台读取用户输入的消息
        client_socket.sendall(message.encode())  # 将消息编码并发送给服务器
        time.sleep(1)
def receive_message(client_socket):
    """接收服务器的消息"""
    while True:
        data = client_socket.recv(BUFFER_SIZE)  # 接收服务器发送的消息
        if not data:  # 如果没有接收到任何数据说明连接已经断开
            break
        else:
            print(f'Received message from server: {data.decode()}')

# 使用线程池管理线程
with ThreadPoolExecutor(max_workers=5) as executor:
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.connect((HOST, PORT))  # 连接服务器

        futures = []
        # 将任务提交到线程池中
        futures.append(executor.submit(send_message, s))
        futures.append(executor.submit(receive_message, s))

        # 等待所有任务完成
        for future in futures:
            future.result()