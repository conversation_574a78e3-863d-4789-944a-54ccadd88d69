import time

from selenium import webdriver
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.edge.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 初始化Edge WebDriver
edge_path = r'D:\pystorage\msedgedriver.exe'  # 替换为您的WebDriver路径
service = Service(edge_path)  # 创建Service对象

# 创建Edge WebDriver实例
driver = webdriver.Edge(service=service)
driver.maximize_window()

# 打开网站
driver.get("https://www.luffycity.com/")

# 等待登录按钮出现并点击
login_button = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((By.XPATH, "/html/body/div[3]/div/div/header/div/div/div[2]/div[2]/span"))
)
login_button.click()

# 等待登录窗口弹出
# 如果有iframe需要切换，使用以下代码（根据实际情况调整iframe的selector）
# iframe = WebDriverWait(driver, 10).until(EC.frame_to_be_available_and_switch_to_it((By.ID, "iframe_id")))

# 等待登录名和密码输入框出现
username_input = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((By.XPATH, "/html/body/div[3]/div/div/div[2]/div/div/div[1]/div[1]/input"))
)
password_input = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((By.XPATH, "/html/body/div[3]/div/div/div[2]/div/div/div[1]/div[2]/input"))
    # 注意：这里我假设密码输入框的xpath是类似登录名的，您需要根据实际情况调整
)

# 输入登录名和密码
username = '15618899597'
password = 'lufeixuecheng'
username_input.send_keys(username)
password_input.send_keys(password)

# 点击登录按钮
# 这里假设登录按钮的xpath是 "/html/body/div[3]/div/div/div[2]/div/div/button"
# 请根据实际页面结构替换为正确的xpath
login_submit_button = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((By.XPATH, "/html/body/div[3]/div/div/div[2]/div/div/button"))
)
login_submit_button.click()
#登录后

myclass = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((By.XPATH, "/html/body/div[3]/div/div/header/div/div/div[2]/div[2]/div/div[1]/a"))
)
myclass.click()

chapter9 = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((By.XPATH, "/html/body/div[3]/div/div/div[1]/main/div[2]/div/div/div[1]/ul[3]/li[9]"))
)
chapter9.click()
learnmore = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((By.XPATH, "/html/body/div[3]/div/div/div[1]/main/div[2]/div/div/div[2]/section/p[5]/button"))
)
learnmore.click()

while 1:
    time.sleep(5)
    try:
        nextlesson = WebDriverWait(driver, 7200).until(
            EC.element_to_be_clickable((By.XPATH, "/html/body/div[3]/div/div/div/div[1]/div[3]/div/p[3]"))
        )
        actions = ActionChains(driver)
        actions.move_to_element(nextlesson)
        time.sleep(5)
        actions.click()
        actions.perform()
    except :
        pass





