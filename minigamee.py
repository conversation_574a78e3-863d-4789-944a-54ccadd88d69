import requests
import hashlib, json
from datetime import datetime


def generate_body_signature(query, secret_key):
    if not type(query) == str:
        body = ''.join(str(query[key]) for key in sorted(query))
    else:
        body = query
    sign = hashlib.md5((body + secret_key).encode('utf-8')).hexdigest().lower()
    return sign


class ApiClient:
    def __init__(self, baseurl, api_key=None, gameId=None, accesstoken=None):
        self.baseurl = baseurl
        self.api_key = api_key
        self.gameId = gameId
        self.headers = {
            'gameId': self.gameId,
            'requestId': 'hunter01',
            'accessToken': accesstoken
        }

    # 1. 验证登录凭证
    def validate_token(self, code):
        url = self.baseurl + 'unityGame/token'
        query = {'code': code}
        sign = generate_body_signature(query, self.api_key)
        self.headers['sign'] = sign
        response = requests.post(url, data=query, headers=self.headers)
        self.headers['accessToken'] = response.json()['result']['accessToken']
        print(self.headers)
        return response.json()

    # 2. 获取用户信息
    def get_user_info(self, user_ids):
        url = self.baseurl + 'unityGame/user'
        query = {'userIds': user_ids}
        sign = generate_body_signature(query, self.api_key)
        self.headers['sign'] = sign
        response = requests.get(url, params=query, headers=self.headers)
        print(response.text)

    # 3. 获取好友列表
    def get_friends(self):
        url = self.baseurl + 'unityGame/friends'
        sign = generate_body_signature({}, self.api_key)
        self.headers['sign'] = sign
        response = requests.get(url, headers=self.headers)
        print(response.text)

    # 4. 货币兑换
    def exchange_currency(self, trade_no, exchange_amount):
        url = self.baseurl + 'unityGame/exchange'
        query = {
            'tradeNo': trade_no,
            'exchangeAmount': exchange_amount
        }
        sign = generate_body_signature(query, self.api_key)
        self.headers['sign'] = sign
        response = requests.post(url, data=query, headers=self.headers)
        print(response.text)

    # 5. 获取用户狼人杀账户余额
    def get_balance(self):
        url = self.baseurl + 'unityGame/account/balance'
        sign = generate_body_signature({}, self.api_key)
        self.headers['sign'] = sign
        response = requests.get(url, headers=self.headers)
        print(response.text)
        # return response.json()

    # 6. 直购预创单
    def prepay(self, order_no, product_id=None, amount=None, body=None, subject=None, callback_param=None):
        url = self.baseurl + 'unityGame/direct/purchase/prepay'
        data = {
            "orderNo": order_no,
            "productId": product_id,
            'amount': amount,
            'body': body,
            'subject': subject,
            "callbackParam": callback_param
        }
        data = {k: v for k, v in data.items() if v is not None}
        sign = generate_body_signature(json.dumps(data), self.api_key)
        self.headers['sign'] = sign
        response = requests.post(url, json=data, headers=self.headers)
        print(response.text)

    # 7. 充值订单状态查询
    def check_order_status(self, order_no):
        url = self.baseurl + 'unityGame/direct/purchase/order'
        query = {'orderNo': order_no}
        sign = generate_body_signature(query, self.api_key)
        self.headers['sign'] = sign
        response = requests.get(url, params=query, headers=self.headers)
        print(response.text)

    # 直购订单状态通知
    def notify_purchase_status(self, order_no, game_id, user_id, order_status, timestamp, product_id=None, amount=None,
                               callback_param=None):
        url = self.baseurl + 'direct/purchase/status'
        data = {
            "orderNo": order_no,
            "gameId": game_id,
            "userId": user_id,
            "productId": product_id,
            "amount": amount,
            "orderStatus": order_status,
            "timestamp": timestamp,
            "callbackParam": callback_param
        }
        response = requests.post(url, json=data, headers=self.headers)
        print(response.text)
        return response.json()

    def redpoint(self, user_id, eventid, expiretime):
        url = self.baseurl + 'unityGame/user/red/dot/notify'
        data = {"userId": user_id,
                "eventId": eventid,
                "redDot": True,
                "tip":'红点提示',
                "expireTime": int(datetime.strptime(expiretime, "%Y-%m-%d %H:%M:%S").timestamp() * 1000)
                }
        sign = generate_body_signature(json.dumps(data), self.api_key)
        self.headers['sign'] = sign
        response = requests.post(url, json=data, headers=self.headers)
        print(response.text)

    def addfriend(self,userid):
        url=self.baseurl+'unityGame/friend/request'
        data={"userId":userid}
        sign = generate_body_signature(data, self.api_key)
        self.headers['sign'] = sign
        response=requests.post(url,data=data,headers=self.headers)
        print(response.text)

# 示例调用
if __name__ == "__main__":
    # qa1的14725836909
    # d = ApiClient(baseurl='http://qa-rancher.langren001.com/langren/external/',
    #               api_key='bEYQjrpTElzzTPA9HLzqBPVhF7iZUjDN', gameId='lrs_qa_21721118731',
    #               accesstoken='2b43fdbe34e61709af7a11cb990305c8')
    # d.validate_token('17dc090fa6934e529d37f2eb9f095e8d')
    # d.get_balance()
    # d.get_user_info('7605')
    # d.get_friends()
    # d.redpoint(7602, '10003', '2024-07-25 17:47:00')
    # d.check_order_status('0722ios052')

    # qa1的14725836910
    e = ApiClient(baseurl='http://qa-rancher.langren001.com/langren/external/',
                  api_key='bEYQjrpTElzzTPA9HLzqBPVhF7iZUjDN', gameId='lrs_qa_21721118731',
                  accesstoken='085e509b01bfdc9044130e6bb9821504')
    # # e.validate_token('cb273e824d73466481d2e57dcc11a2d1')
    # e.get_balance()
    # e.get_user_info('7603')
    # e.get_friends()
    # e.addfriend('7609')
    # e.redpoint(7603, '10003', '2024-07-25 10:57:00')

    # qa3的14725836910
    f = ApiClient(baseurl='http://lrqa.langren001.com/langren/external/',
                  api_key='9HvO0aTBdierSJiZ6fFcBzuod9KgpOP4', gameId='lrs_qa_51720074617',
                  accesstoken='c4a9f06cedc8fb48336ddc9d98369e69')
    # f.validate_token('05ea6fcd5ad74501b5b539485c05be06')
    # f.get_balance()
    # f.get_user_info('7603')
    # f.get_friends()
    # f.addfriend(f'7506')
    e.prepay(f'051505', amount=0.01, body='qa3order1', subject='skyint22o1', callback_param='fight3331')
    # f.prepay(f'99002',product_id='com.c2vl.kgamebox.lb12.v1',body='tianshen',subject='monv',callback_param='jungle433')
    # f.redpoint(7602, '10003', '2024-12-24 10:57:00')
    # f.check_order_status('99010')

