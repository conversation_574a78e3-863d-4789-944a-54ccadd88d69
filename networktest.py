import subprocess
import platform
import time
import socket
import ssl
import pprint
from datetime import datetime
import requests
import dns.resolver
from scapy.all import sr1, IP, ICMP

host = '163.com'


def system_ping(host, count=4, packetsize=64):
    param = '-n' if platform.system().lower() == 'windows' else '-c'
    size_param = '-l' if platform.system().lower() == 'windows' else '-s'
    command = ['ping', param, str(count), size_param, str(packetsize), host]
    try:
        return subprocess.check_output(command, stderr=subprocess.STDOUT, universal_newlines=True)
    except subprocess.CalledProcessError as e:
        return e.output


def tcp_ping(host, port, timeout=1):
    start_time = time.time()
    try:
        with socket.create_connection((host, port), timeout=timeout):
            end_time = time.time()
            return f"{end_time - start_time:.2f}"
    except (socket.timeout, socket.error) as e:
        return f"failed: {e}"


import ssl
import socket
import pprint
from datetime import datetime
from OpenSSL import crypto

import ssl
import socket
from datetime import datetime


def get_certificate_info(host, port=443):
    # 创建默认上下文
    context = ssl.create_default_context()

    # 尝试连接到服务器并获取证书
    with socket.create_connection((host, port)) as sock:
        with context.wrap_socket(sock, server_hostname=host) as ssock:
            certificate = ssock.getpeercert()

    # 打印证书内容
    print(certificate)

    # 解析证书中的日期
    not_before = datetime.strptime(certificate['notBefore'], '%b %d %H:%M:%S %Y %Z')
    not_after = datetime.strptime(certificate['notAfter'], '%b %d %H:%M:%S %Y %Z')

    # 解析证书颁发者信息
    issuer = dict(x[0] for x in certificate['issuer'])
    issued_by = issuer.get('organizationName')

    # 打印证书有效期和颁发机构
    print(f"证书有效期: 从 {not_before} 到 {not_after}")
    print(f"颁发机构: {issued_by}")

    # 验证证书是否可信
    try:
        # context.verify_mode = ssl.CERT_REQUIRED 已经在 create_default_context() 中设置
        # 只要 wrap_socket() 没有抛出异常，那么证书就是可信的
        print(f"证书 {host}:{port} 是可信的。")
    except ssl.CertificateError as e:
        print(f"证书 {host}:{port} 不可信。原因: {e}")
    except ssl.SSLError as e:
        print(f"SSL 错误: {e}")


# 使用函数
# get_certificate_info('www.baidu.com')

def dns_probe(hostname):
    resolver = dns.resolver.Resolver()
    try:
        start_time = time.time()
        answers = resolver.resolve(hostname, 'A')
        end_time = time.time()
        print(f"DNS resolution for {hostname} took {end_time - start_time} seconds.")
        for rdata in answers:
            print(rdata.address)
        if answers.rrset is not None:
            print(f"TTL: {answers.rrset.ttl}")
    except (dns.resolver.NoAnswer, dns.resolver.NXDOMAIN, dns.resolver.Timeout) as e:
        print(f"DNS resolution error: {e}")


dns_probe('www.baidu.com:54')
def http_probe(url):
    url = 'http://' + url
    try:
        start_time = time.time()
        response = requests.get(url)
        end_time = time.time()
        print(f"HTTP request to {url} took {end_time - start_time} seconds.")
        print(f"Status Code: {response.status_code}")
        for key, value in response.headers.items():
            print(f"{key}: {value}")
        print("\nResponse content (first 200 characters):")
        print(response.text[:200])
    except requests.exceptions.RequestException as e:
        print(f"HTTP request error: {e}")


def mtr(target, max_hops=30, timeout=2):
    ttl = 1
    reached_destination = False
    while not reached_destination and ttl <= max_hops:
        packet = IP(dst=target, ttl=ttl) / ICMP()
        reply = sr1(packet, verbose=0, timeout=timeout)
        if reply is None:
            print(f"{ttl}\tNo reply")
        else:
            src_ip = reply.src
            icmp_type = reply.getlayer(ICMP).type
            if icmp_type == 0:
                print(f"{ttl}\t{src_ip}\tReached destination")
                reached_destination = True
            elif icmp_type == 11:
                print(f"{ttl}\t{src_ip}")
            else:
                print(f"{ttl}\t{src_ip}\tUnknown ICMP type {icmp_type}")
        ttl += 1
        time.sleep(1)


# # Usage examples
# print(system_ping(host, count=5))
# print('*' * 50)
#
# result = tcp_ping(host, port=80)
# print(
#     f"TCP ping to {host}:80 took {result} seconds." if "failed" not in result else f"TCP ping failed: {result.split(': ')[1]}")
# print('*' * 50)
#
# get_certificate_info(host)
# print('*' * 50)
#
# dns_probe(host)
# print('*' * 50)
#
# http_probe(host)
# print('*' * 50)

mtr(host)
