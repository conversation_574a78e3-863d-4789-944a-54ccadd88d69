import subprocess, threading, hashlib
import requests, json, time, datetime, random, os

# 需安装ffmpeg,参考网址 https://blog.csdn.net/HYEHYEHYE/article/details/122000352

headers = dict()


# 获取验证码
def getcode(ph):
    url = host + '/client/security/verificationCode'
    params = {'codeVerificationType': '0',
              'phoneNumber': ph}
    requests.get(url, params=params, headers=headers)


# 手机登录
def login(ph):
    url = host + '/client/security/login/phone'
    params = {'phoneNumber': ph,
              'verificationCode': '1234'}
    response = requests.post(url, data=params, headers=headers)
    token = json.loads(response.text)['result']['token']
    return token


# 获取第三方平台信息
def getlist(ttoken):
    url = host + '/client/platform/list'
    headers = {'live-client-token': ttoken}
    requests.get(url, headers=headers)


# 进入平台
def enter(ttoken, platform):
    url = host + '/client/platform/enter'
    headers = {'live-client-token': ttoken,
               'live-platform-id': platform[0]}
    response = requests.get(url, headers=headers)
    print(response.text)


# 检查直播状态是否直播是否推流
def getstatus(ttoken, platform):
    url = host + '/client/platform/live/status'
    headers = {'live-client-token': ttoken,
               'live-platform-id': platform[0]}
    response = requests.get(url, headers=headers)
    return response.text


# 获取推流码
def getstream(ttoken, platform):
    url = host + '/client/platform/live/stream/info'
    headers = {'live-client-token': ttoken,
               'live-platform-id': platform[0]}
    requests.get(url, headers=headers)


# 开始直播
def startlive(ttoken, platform, ph):
    url = host + '/client/platform/live/start'
    headers = {'live-client-token': ttoken,
               'live-platform-id': platform[0]}
    while 1:
        response = requests.post(url, headers=headers)
        print(f'开播返回{ph}：{response.text}')
        if json.loads(response.text)['code'] == 0:
            m1 = json.loads(response.text)['result']['pushUrl']
            m2 = json.loads(response.text)['result']['pushAuthKey']
            m3 = m1 + "/" + m2
            return m3
        time.sleep(1)


# 停播
def stoplive(ttoken, platform):
    url = host + '/client/platform/live/stop'
    headers = {'live-client-token': ttoken,
               'live-platform-id': platform[0]}
    response = requests.post(url, headers=headers)
    if json.loads(response.text)['result'] == True: print('已关播')


# 设置房间
def setroom(ttoken, ph, platform):
    url = host + '/client/platform/room/setting'
    headers = {
        'live-client-token': ttoken,
        'live-platform-id': platform[0],
        'Content-Type': 'application/json'
    }
    roomname = f'开心游戏{ph[-2:]}'
    data = {
        "notice": roomname,
        "remember": 1,
        "roomCover": "https://live-middle.oss-cn-hangzhou.aliyuncs.com/qa/img/8B69EDF219484BF88731C226875B185F.jpeg",
        "roomName": roomname
    }
    json_data = json.dumps(data)
    response = requests.post(url, headers=headers, data=json_data)
    print(f'设置房间返回{ph}：{response.text}')


# 开始挂载
def gamestart(ttoken, platform, appid):
    url = host + '/client/platform/room/game/mount'
    headers = {'live-client-token': ttoken,
               'live-platform-id': platform[0]}
    params = {'appId': appid[0],
              'livePageConfigId': '4'}
    response = requests.post(url, headers=headers, data=params)
    print(response.text)
    code = json.loads(response.text)['result']
    return (ttoken, appid, code)


# 停止挂载
def gameover(ttoken, appid, platform):
    url = host + '/client/platform/room/game/unmount'
    headers = {'live-client-token': ttoken,
               'live-platform-id': platform[0]}
    params = {'appId': appid[0],
              'livePageConfigId': '2'}
    response = requests.post(url, headers=headers, data=params)
    print(response.text)


# 验签
def addsign(params, secret_key):
    sorted_keys = sorted(params.keys())  # 按键排序
    values = [str(params[key]) for key in sorted_keys]  # 获取值列表
    signature = ''.join(values) + secret_key  # 拼接值并加上密钥
    sign_value = hashlib.md5(signature.encode()).hexdigest()  # 计算MD5摘要
    headers['live-signature'] = sign_value  # 将签名值加入header


# cp获取token
def getaccesstoken(code, appid):
    url = host + '/cp/security/token'
    params = {'appId': appid[0],
              'code': code,
              'grant_type': "authorization_code"}
    print(params)
    addsign(params, appid[1])
    response = requests.post(url, headers=headers, data=params)
    print(response.text)
    return json.loads(response.text)['result']


# cp更新token
def cpnewtoken(refreshtoken, appid):
    url = host + '/cp/security/token'
    params = {'appId': appid[0],
              'refresh_token': refreshtoken,
              'grant_type': "refresh_token"}
    addsign(params, appid[1])
    response = requests.post(url, headers=headers, data=params)
    print(response.text)
    return json.loads(response.text)['result']['refreshToken']


# cp开始任务
def cpstart(code, cptoken, appid):
    url = host + '/cp/task/start'
    params = {'appId': appid[0],
              'roomCode': code,
              # 'buttonOption':0,
              }
    ns = appid[0] + code + appid[1]
    sign = hashlib.md5(ns.encode()).hexdigest()
    headers = {'live-signature': sign,
               'access_token': cptoken}
    response = requests.post(url, headers=headers, data=params)
    print(response.text)


# cp查询
def cpstatus(code, cptoken, appid):
    url = host + '/cp/task/status'
    params = {'appId': appid[0],
              'roomCode': code,
              }
    ns = appid[0] + code + appid[1]
    sign = hashlib.md5(ns.encode()).hexdigest()
    headers = {'live-signature': sign,
               'access_token': cptoken}
    response = requests.get(url, headers=headers, params=params)
    print(response.text)


# cp结束
def cpstop(code, cptoken, appid):
    url = host + '/cp/task/stop'
    params = {'appId': appid[0],
              'roomCode': code,
              }
    ns = appid[0] + code + appid[1]
    sign = hashlib.md5(ns.encode()).hexdigest()
    headers = {'live-signature': sign,
               'access_token': cptoken}
    response = requests.post(url, headers=headers, data=params)
    print(response.text)


# 开播推流挂载流程
def onelive(ph, file, appid, todo=0):
    getcode(ph)
    pctoken = login(ph)
    enter(pctoken, platform)
    livestatus = getstatus(pctoken, platform)
    if json.loads(livestatus)['result']['live'] == 1:
        stoplive(pctoken, platform)
    setroom(pctoken, ph, platform)
    pushurl = startlive(pctoken, platform, ph)

    def temppush(pushurl):
        command = [
            'ffmpeg',
            '-stream_loop', '-1',
            '-re',  # 以实际帧率读取视频
            '-i', file,  # 将输入文件改为你的视频文件地址
            '-c:v', 'copy',  # 直接复制视频流，不重新编码
            '-c:a', 'aac',  # 将音频编码为AAC
            '-ar', '44100',  # 设置音频采样率
            '-strict', 'experimental',
            '-f', 'flv',  # 指定输出格式为FLV
            pushurl  # 你的RTMP URL
        ]
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            print(f'Error executing command: {stderr.decode()}')

    thread = threading.Thread(target=temppush, args=(pushurl,))
    thread.start()
    timer = threading.Timer(3600, stoplive, args=(pctoken, platform))
    print(f'定时播放已开启，3600s后关播。现在{datetime.datetime.now()}')
    timer.start()
    if todo == 1:
        gamecode = gamestart(pctoken, platform, appid)[-1]
        # cptokens = getaccesstoken(gamecode, appid)
        # cpstart(gamecode, cptokens['accessToken'], appid)
        # everytoken = cptokens['refreshToken']
        # while 1:
        #     print(f'1.5小时后更新cpfreshtoken{datetime.datetime.now()}')
        #     time.sleep(90 * 60)
        #     everytoken = cpnewtoken(everytoken, appid)


def get_random_file_path(folder_path):
    file_paths = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_paths.append(file_path)
    random_file_path = random.choice(file_paths)
    return random_file_path


# 挂载游戏todo=1
def run(phs, files, appids, todo):
    for ph in phs:
        file = get_random_file_path(files)
        appid = random.choice(appids)
        thread = threading.Thread(target=onelive, args=(ph, file, appid, todo))
        thread.start()


platforms = [['pt69582870', 'U0CxFXosNZlkgEncvG0KRhffKEEswROZ'],  # 弹丸qa
             ['pt31120714', 'mI4bwbAiKrXgQ7yygpJnYWJc77CbfSJe'],  # 狼人杀qa1
             ['pt02038168', 'pK5oZvfqAzAe4Gb4zEDgcBJbMX2Sq3z7'],  # 狼人杀沙盒
             ['pt87345622', 'pKwPMDpjBck7IFGJxLSq1GNIIq1httbL'],  # 弹丸沙盒
             ['langren3', 'M7fF9kcinyRUySnLGaGWkRmC2FliXRWs'],  # 狼人杀qa3
             ]
appids = [
    # ['yg91688886', 'H517stt3ZWS1nbGuNWw9hTQnXtG5D7xx'],  # demo小游戏qa
    # ['yg17823998', 'GcX80tqv7bLTZeeGCqSA9gWq5yNt65Hr'],  # 丛林求生qa
    # ['yg10226371', 'gAA2bN0geJJJcbrCqO4oqQGdrZGbFsvk'],  # 都市求生qa
    # ['yg33863447', 'FyuVgOIgaRvGAbh9Bzsw059bAONSVcbN'],  # demo小游戏沙盒
    # ['yg85322668', 'mmYxUkPzSfhf2xevhx91lN37xjcva4be'],  # 黄色闪光
    ['yg58366721','DHRPiZ7ZRRQMwUkadJ6cRh2cHqM7ZW7O']
]
# host = 'https://qa.live.caniculab.com/live'  # qa环境
host = 'https://sd.live.caniculab.com/live' # 沙盒环境

phs = ['14725836999']
# phs = [f'155500000{x}' for x in range(90, 96)]  # 对应主播手机号，
files = r'D:\pystorage\SmallTools\videos'  # 推流视频文件夹
platform = platforms[-1]  # 主播平台选择
# 挂载游戏todo=1 仅直播todo=0
run(phs, files, appids, 1)
