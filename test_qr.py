#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试二维码生成功能
"""

try:
    import qrcode
    from io import BytesIO
    print("✓ qrcode 库导入成功")
    
    # 测试生成二维码
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    
    qr.add_data("http://127.0.0.1:7000")
    qr.make(fit=True)
    
    img = qr.make_image(fill_color="black", back_color="white")
    print("✓ 二维码生成成功")
    
    # 测试保存到内存
    img_io = BytesIO()
    img.save(img_io, 'PNG')
    img_io.seek(0)
    print("✓ 二维码保存到内存成功")
    print(f"图片大小: {len(img_io.getvalue())} 字节")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    print("请运行: pip install qrcode[pil]")
except Exception as e:
    print(f"✗ 测试失败: {e}")
