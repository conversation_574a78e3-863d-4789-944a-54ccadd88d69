@echo off
chcp 65001
echo 正在安装必要的依赖...
pip install nuitka
pip install flask
pip install wheel
pip install setuptools

echo 开始打包...
python -m nuitka --follow-imports ^
    --enable-plugin=tk-inter ^
    --include-package=flask ^
    --include-package=werkzeug ^
    --include-package=jinja2 ^
    --include-data-dir=templates=templates ^
    --output-dir=build ^
    --onefile ^
    --output-filename=up^&down.exe ^
    --include-data-files=templates/*.html=templates/ ^
    --mingw64 ^
    --jobs=1 ^
    --low-memory ^
    --show-progress ^
    --show-memory ^
    --assume-yes-for-downloads ^
    --no-pyi-file ^
    --remove-output ^
    --standalone ^
    --windows-disable-console ^
    --lto=no ^
    app.py

echo 打包完成！exe文件位置：build\up^&down.exe
pause 